import { createMiddleware } from "hono/factory";

export function trimTrailingSlash() {
  return createMiddleware(async (ctx, next) => {
    if (
      (ctx.req.method === "GET" || ctx.req.method === "HEAD") &&
      ctx.req.path !== "/" &&
      ctx.req.path.at(-1) === "/"
    ) {
      const url = new URL(ctx.req.url);
      url.pathname = url.pathname.substring(0, url.pathname.length - 1);
      ctx.res = ctx.redirect(url.toString(), 301);
    }

    return next();
  });
}
