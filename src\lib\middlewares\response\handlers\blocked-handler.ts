import { HttpError } from "@/shared/key-management/error";

import { type Hand<PERSON> } from "@/lib/middlewares/create-proxy";

export const handleBlockedResponse: Handler = async ({ ctx, proxyResponse }) => {
  const contentType = proxyResponse.headers.get("Content-Type");
  if (!contentType?.startsWith("application/json")) {
    throw new HttpError(500, `Upstream error: Expected JSON response. Got: ${contentType}`);
  }

  if (!proxyResponse.body) {
    throw new HttpError(500, "Upstream error: No response body");
  }

  const response = proxyResponse.clone();

  ctx.var.afterResponsePromises.push(async () => {
    const responseBody = await proxyResponse.clone().json();
    ctx.set("fullResponseBody", responseBody);
  });

  return new Response(response.body, response);
};
