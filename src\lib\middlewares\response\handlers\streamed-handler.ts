import { HttpError } from "@/shared/key-management/error";
import type { LLM_Providers } from "@/shared/providers";

import { type <PERSON><PERSON> } from "@/lib/middlewares/create-proxy";
import type {
  DeepSeekChatCompletionStreamResponse,
  GeminiStreamGenerateContentResponse,
  OpenAIChatCompletionStreamResponse,
  ResponseBody,
  ResponseChunk,
} from "@/lib/schemas";

import { handleBlockedResponse } from "./blocked-handler";

export const handleStreamedResponse: Handler = async ({ ctx, proxyResponse }) => {
  const contentType = proxyResponse.headers.get("Content-Type");

  if (!proxyResponse.body) {
    throw new HttpError(500, "Upstream error: No response body");
  }

  if (contentType?.startsWith("application/json")) {
    ctx.var.logger.warn(
      { contentType },
      "Upstream returned JSON response instead of SSE. Falling back to blocked response.",
    );
    return handleBlockedResponse({ ctx, proxyResponse });
  }

  if (!contentType?.startsWith("text/event-stream")) {
    throw new HttpError(500, `Upstream error: Expected SSE response. Got: ${contentType}`);
  }

  const response = proxyResponse.clone();
  const [stream, serverStream] = response.body!.tee();

  ctx.var.afterResponsePromises.push(async () => {
    const rawEvents = await streamToString(serverStream);

    const body = mergeEventChunksIntoSingleObject(
      rawEvents.split("\r\n"),
      ctx.var.requestData!.provider,
    );

    if (!body) return;
    ctx.set("fullResponseBody", body);
  });

  return new Response(stream, response);
};

function streamToString(stream: ReadableStream<Uint8Array>): Promise<string> {
  return new Promise(function (resolve, reject) {
    const reader = stream.getReader();
    const decoder = new TextDecoder();
    let result = "";

    function read() {
      reader
        .read()
        .then(function ({ done, value }) {
          if (done) return resolve(result);
          result += decoder.decode(value, { stream: true });
          read();
        })
        .catch(reject);
    }

    read();
  });
}

function mergeEventChunksIntoSingleObject(
  rawEvents: string[],
  provider: LLM_Providers,
): ResponseBody | undefined {
  const chunks = rawEvents
    .filter((e) => e.startsWith("data: "))
    .map((event) => {
      const data = event.substring("data: ".length);
      if (data === "[DONE]") return;
      return JSON.parse(data);
    });

  if (!chunks.length) return;

  if (isOpenAIStream(chunks, provider)) {
    const lastChunk = chunks[chunks.length - 1]!;

    return {
      choices: [],
      object: "chat.completion",
      created: lastChunk.created,
      model: lastChunk.model,
      usage: lastChunk.usage,
      id: lastChunk.id,
    };
  }

  if (isDeepSeekStream(chunks, provider)) {
    const lastChunk = chunks[chunks.length - 1]!;

    return {
      choices: [],
      object: "chat.completion",
      created: lastChunk.created,
      model: lastChunk.model,
      usage: lastChunk.usage,
      id: lastChunk.id,
    };
  }

  if (isGeminiStream(chunks, provider)) {
    const lastChunk = chunks[chunks.length - 1]!;

    return {
      candidates: [],
      modelVersion: lastChunk.modelVersion,
      usageMetadata: lastChunk.usageMetadata,
    };
  }
}

function isOpenAIStream(
  chunks: ResponseChunk[],
  provider: LLM_Providers,
): chunks is OpenAIChatCompletionStreamResponse[] {
  return !!chunks[0] && "choices" in chunks[0] && provider === "openai";
}

function isGeminiStream(
  chunks: ResponseChunk[],
  provider: LLM_Providers,
): chunks is GeminiStreamGenerateContentResponse[] {
  return !!chunks[0] && "candidates" in chunks[0] && provider === "gemini";
}

function isDeepSeekStream(
  chunks: ResponseChunk[],
  provider: LLM_Providers,
): chunks is DeepSeekChatCompletionStreamResponse[] {
  return !!chunks[0] && "choices" in chunks[0] && provider === "deepseek";
}
