import { Hono } from "hono";

import { deepseek } from "./providers/deepseek";
import { gemini } from "./providers/gemini";
import { openai } from "./providers/openai";

import { authenticateMiddleware } from "@/lib/middlewares/hono/authenticate";
import { prefixV1 } from "@/lib/middlewares/hono/prefix-v1";

const proxyRoute = new Hono();
proxyRoute.use(authenticateMiddleware());

proxyRoute
  .use(prefixV1())
  .route("/openai", openai)
  .route("/gemini", gemini)
  .route("/deepseek", deepseek);

export { proxyRoute };
