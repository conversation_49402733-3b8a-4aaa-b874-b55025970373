import { and, desc, asc, eq, sql } from "drizzle-orm";
import z from "zod";

import { protectedProcedure, t } from "..";

import { schema } from "@/shared/database";
import { LLM_PROVIDERS } from "@/shared/providers";

// Input validation schemas
const logsListInputSchema = z.object({
  // Filtering options
  provider: z.enum(LLM_PROVIDERS).optional(),
  modelFamilyId: z.string().optional(),
  keyHash: z.string().optional(),

  // Sorting options
  sortBy: z.enum(["oldest", "newest", "most_expensive", "most_tokens"]).default("newest"),

  // Pagination
  page: z.number().int().min(1).default(1),
  pageSize: z.number().int().min(1).max(100).default(20),
});

export const logsRouter = t.router({
  list: protectedProcedure.input(logsListInputSchema).query(async ({ ctx, input }) => {
    const { provider, modelFamilyId, keyHash, sortBy, page, pageSize } = input;
    const offset = (page - 1) * pageSize;

    // Build where conditions
    const whereConditions = [];
    if (provider) {
      whereConditions.push(eq(schema.logs.provider, provider));
    }
    if (modelFamilyId) {
      whereConditions.push(eq(schema.logs.modelFamilyId, modelFamilyId));
    }
    if (keyHash) {
      whereConditions.push(eq(schema.logs.keyHash, keyHash));
    }

    const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined;

    // Build order by clause
    let orderByClause;
    switch (sortBy) {
      case "oldest":
        orderByClause = [asc(schema.logs.createdAt)];
        break;
      case "newest":
        orderByClause = [desc(schema.logs.createdAt)];
        break;
      case "most_expensive":
        // Order by calculated cost (inputTokens * inputCost + outputTokens * outputCost + reasoningTokens * outputCost)
        orderByClause = [
          desc(
            sql`(${schema.logs.inputTokens} * ${schema.modelFamilies.inputCost} / 1000000.0 +
                   (${schema.logs.outputTokens} + ${schema.logs.reasoningTokens}) * ${schema.modelFamilies.outputCost} / 1000000.0)`,
          ),
        ];
        break;
      case "most_tokens":
        // Order by total tokens (input + output + reasoning)
        orderByClause = [
          desc(
            sql`(${schema.logs.inputTokens} + ${schema.logs.outputTokens} + ${schema.logs.reasoningTokens})`,
          ),
        ];
        break;
      default:
        orderByClause = [desc(schema.logs.createdAt)];
    }

    // Get logs with relations
    const logs = await ctx.db
      .select({
        id: schema.logs.id,
        provider: schema.logs.provider,
        model: schema.logs.model,
        inputTokens: schema.logs.inputTokens,
        outputTokens: schema.logs.outputTokens,
        reasoningTokens: schema.logs.reasoningTokens,
        createdAt: schema.logs.createdAt,

        // Model family data
        modelFamilyId: schema.logs.modelFamilyId,
        modelFamilyName: schema.modelFamilies.name,
        inputCost: schema.modelFamilies.inputCost,
        outputCost: schema.modelFamilies.outputCost,

        // Key data (hash only for privacy)
        keyHash: schema.logs.keyHash,

        // User data
        userId: schema.logs.userId,
        username: schema.users.username,

        // Calculated fields
        totalTokens: sql<number>`(${schema.logs.inputTokens} + ${schema.logs.outputTokens} + ${schema.logs.reasoningTokens})`,
        totalCost: sql<number>`(${schema.logs.inputTokens} * ${schema.modelFamilies.inputCost} / 1000000.0 +
                                   (${schema.logs.outputTokens} + ${schema.logs.reasoningTokens}) * ${schema.modelFamilies.outputCost} / 1000000.0)`,
      })
      .from(schema.logs)
      .leftJoin(schema.modelFamilies, eq(schema.logs.modelFamilyId, schema.modelFamilies.id))
      .leftJoin(schema.keys, eq(schema.logs.keyHash, schema.keys.hash))
      .leftJoin(schema.users, eq(schema.logs.userId, schema.users.id))
      .where(whereClause)
      .orderBy(...orderByClause)
      .limit(pageSize)
      .offset(offset);

    // Get total count for pagination
    const totalCountResult = await ctx.db
      .select({ count: sql<number>`count(*)` })
      .from(schema.logs)
      .leftJoin(schema.modelFamilies, eq(schema.logs.modelFamilyId, schema.modelFamilies.id))
      .where(whereClause);

    const totalCount = totalCountResult[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      logs,
      pagination: {
        page,
        pageSize,
        totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }),

  // Get filter options for dropdowns
  getFilterOptions: protectedProcedure.query(async ({ ctx }) => {
    // Get unique providers
    const providers = await ctx.db
      .selectDistinct({ provider: schema.logs.provider })
      .from(schema.logs);

    // Get model families that have logs
    const modelFamilies = await ctx.db
      .selectDistinct({
        id: schema.modelFamilies.id,
        name: schema.modelFamilies.name,
        provider: schema.modelFamilies.provider,
      })
      .from(schema.modelFamilies)
      .innerJoin(schema.logs, eq(schema.logs.modelFamilyId, schema.modelFamilies.id))
      .orderBy(schema.modelFamilies.name);

    // Get API keys that have logs (showing only hash prefix for privacy)
    const apiKeys = await ctx.db
      .selectDistinct({
        hash: schema.keys.hash,
        provider: schema.keys.provider,
      })
      .from(schema.keys)
      .innerJoin(schema.logs, eq(schema.logs.keyHash, schema.keys.hash))
      .orderBy(schema.keys.provider, schema.keys.hash);

    return {
      providers: providers.map((p) => p.provider),
      modelFamilies,
      apiKeys: apiKeys.map((key) => ({
        hash: key.hash,
        provider: key.provider,
        displayName: `${key.provider.toUpperCase()} - ${key.hash.substring(0, 8)}...`,
      })),
    };
  }),

  // Get summary statistics
  getSummary: protectedProcedure.query(async ({ ctx }) => {
    const summary = await ctx.db
      .select({
        totalLogs: sql<number>`count(*)`,
        totalTokens: sql<number>`sum(${schema.logs.inputTokens} + ${schema.logs.outputTokens} + ${schema.logs.reasoningTokens})`,
        totalCost: sql<number>`sum(${schema.logs.inputTokens} * ${schema.modelFamilies.inputCost} / 1000000.0 +
                                   (${schema.logs.outputTokens} + ${schema.logs.reasoningTokens}) * ${schema.modelFamilies.outputCost} / 1000000.0)`,
        avgTokensPerRequest: sql<number>`avg(${schema.logs.inputTokens} + ${schema.logs.outputTokens} + ${schema.logs.reasoningTokens})`,
      })
      .from(schema.logs)
      .leftJoin(schema.modelFamilies, eq(schema.logs.modelFamilyId, schema.modelFamilies.id));

    return (
      summary[0] || {
        totalLogs: 0,
        totalTokens: 0,
        totalCost: 0,
        avgTokensPerRequest: 0,
      }
    );
  }),

  // Get usage statistics for a specific key
  getKeyUsage: protectedProcedure
    .input(z.object({ keyHash: z.string() }))
    .query(async ({ ctx, input }) => {
      const usage = await ctx.db
        .select({
          totalRequests: sql<number>`count(*)`,
          totalInputTokens: sql<number>`sum(${schema.logs.inputTokens})`,
          totalOutputTokens: sql<number>`sum(${schema.logs.outputTokens})`,
          totalReasoningTokens: sql<number>`sum(${schema.logs.reasoningTokens})`,
          totalCost: sql<number>`sum(${schema.logs.inputTokens} * ${schema.modelFamilies.inputCost} / 1000000.0 +
                                     (${schema.logs.outputTokens} + ${schema.logs.reasoningTokens}) * ${schema.modelFamilies.outputCost} / 1000000.0)`,

          // Model family breakdown
          modelFamilyId: schema.logs.modelFamilyId,
          modelFamilyName: schema.modelFamilies.name,
          provider: schema.logs.provider,
          familyRequests: sql<number>`count(*)`,
          familyInputTokens: sql<number>`sum(${schema.logs.inputTokens})`,
          familyOutputTokens: sql<number>`sum(${schema.logs.outputTokens})`,
          familyReasoningTokens: sql<number>`sum(${schema.logs.reasoningTokens})`,
          familyCost: sql<number>`sum(${schema.logs.inputTokens} * ${schema.modelFamilies.inputCost} / 1000000.0 +
                                     (${schema.logs.outputTokens} + ${schema.logs.reasoningTokens}) * ${schema.modelFamilies.outputCost} / 1000000.0)`,
        })
        .from(schema.logs)
        .leftJoin(schema.modelFamilies, eq(schema.logs.modelFamilyId, schema.modelFamilies.id))
        .where(eq(schema.logs.keyHash, input.keyHash))
        .groupBy(schema.logs.modelFamilyId, schema.modelFamilies.name, schema.logs.provider);

      // Calculate totals
      const totals = {
        totalRequests: usage.reduce((sum, row) => sum + row.familyRequests, 0),
        totalInputTokens: usage.reduce((sum, row) => sum + row.familyInputTokens, 0),
        totalOutputTokens: usage.reduce((sum, row) => sum + row.familyOutputTokens, 0),
        totalReasoningTokens: usage.reduce((sum, row) => sum + row.familyReasoningTokens, 0),
        totalCost: usage.reduce((sum, row) => sum + row.familyCost, 0),
      };

      return {
        totals,
        byModelFamily: usage.map((row) => ({
          modelFamilyId: row.modelFamilyId,
          modelFamilyName: row.modelFamilyName,
          provider: row.provider,
          requests: row.familyRequests,
          inputTokens: row.familyInputTokens,
          outputTokens: row.familyOutputTokens,
          reasoningTokens: row.familyReasoningTokens,
          cost: row.familyCost,
        })),
      };
    }),

  // Get most used model family
  getMostUsedModel: protectedProcedure.query(async ({ ctx }) => {
    const result = await ctx.db
      .select({
        modelFamilyId: schema.logs.modelFamilyId,
        modelFamilyName: schema.modelFamilies.name,
        provider: schema.logs.provider,
        totalRequests: sql<number>`count(*)`,
        totalTokens: sql<number>`sum(${schema.logs.inputTokens} + ${schema.logs.outputTokens} + ${schema.logs.reasoningTokens})`,
      })
      .from(schema.logs)
      .leftJoin(schema.modelFamilies, eq(schema.logs.modelFamilyId, schema.modelFamilies.id))
      .groupBy(schema.logs.modelFamilyId, schema.modelFamilies.name, schema.logs.provider)
      .orderBy(sql`count(*) DESC`)
      .limit(1);

    return result[0] || null;
  }),

  // Get daily usage statistics for charts
  getDailyUsage: protectedProcedure
    .input(
      z.object({
        days: z.number().min(1).max(90).default(30),
        provider: z.enum(LLM_PROVIDERS).optional(),
        modelFamilyId: z.string().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { days, provider, modelFamilyId } = input;

      // Build where conditions
      const whereConditions = [];
      if (provider) {
        whereConditions.push(eq(schema.logs.provider, provider));
      }
      if (modelFamilyId) {
        whereConditions.push(eq(schema.logs.modelFamilyId, modelFamilyId));
      }

      const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined;

      const dailyStats = await ctx.db
        .select({
          date: sql<string>`date(${schema.logs.createdAt}, 'unixepoch')`,
          totalRequests: sql<number>`count(*)`,
          totalInputTokens: sql<number>`sum(${schema.logs.inputTokens})`,
          totalOutputTokens: sql<number>`sum(${schema.logs.outputTokens})`,
          totalReasoningTokens: sql<number>`sum(${schema.logs.reasoningTokens})`,
          totalTokens: sql<number>`sum(${schema.logs.inputTokens} + ${schema.logs.outputTokens} + ${schema.logs.reasoningTokens})`,
          totalCost: sql<number>`sum(${schema.logs.inputTokens} * ${schema.modelFamilies.inputCost} / 1000000.0 +
                                     (${schema.logs.outputTokens} + ${schema.logs.reasoningTokens}) * ${schema.modelFamilies.outputCost} / 1000000.0)`,
        })
        .from(schema.logs)
        .leftJoin(schema.modelFamilies, eq(schema.logs.modelFamilyId, schema.modelFamilies.id))
        .where(
          whereClause
            ? and(
                whereClause,
                sql`${schema.logs.createdAt} >= strftime('%s', 'now', '-${days} days')`,
              )
            : sql`${schema.logs.createdAt} >= strftime('%s', 'now', '-${days} days')`,
        )
        .groupBy(sql`date(${schema.logs.createdAt}, 'unixepoch')`)
        .orderBy(sql`date(${schema.logs.createdAt}, 'unixepoch') ASC`);

      return dailyStats;
    }),
});
