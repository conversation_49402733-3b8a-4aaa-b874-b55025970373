import { createMiddleware } from "hono/factory";
import chalk from "chalk";

import { logger } from "@/logger";

export function loggerMiddleware() {
  return createMiddleware(async (ctx, next) => {
    const startTime = performance.now();
    await next();
    const responseTime = (performance.now() - startTime).toFixed(2);

    if (ctx.res.status > 300 && ctx.res.status < 400) return;

    const success = Number(ctx.res.status) < 400;
    const message = success ? "Request complete" : "Request failed";

    const status = success ? chalk.green(ctx.res.status) : chalk.red(ctx.res.status);

    const json = {
      request: {
        method: ctx.req.method,
        url: ctx.req.path,
        query: ctx.req.query(),
        headers: ctx.req.header(),
      },
      response: { statusCode: ctx.res.status, headers: ctx.res.headers },
      responseTime,
    };

    logger.info(
      json,
      `${message} - ${ctx.req.method} ${status} ${ctx.req.path} - ${responseTime}ms`,
    );
  });
}
