import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { createTRPCClient, httpBatchLink, httpLink, splitLink } from "@trpc/client";
import { createTRPCContext } from "@trpc/tanstack-react-query";

import { useState } from "react";
import SuperJSO<PERSON> from "superjson";

import type { Router } from "./root";

export const {
  TRPCProvider: RootTRPCProvider,
  useTRPC,
  useTRPCClient,
} = createTRPCContext<Router>();

function makeQueryClient() {
  return new QueryClient({ defaultOptions: { queries: { staleTime: 60 * 1000 } } });
}

let browserQueryClient: QueryClient | undefined = undefined;
export function getQueryClient() {
  browserQueryClient ??= makeQueryClient();
  return browserQueryClient;
}

export function getTRPCClient() {
  return createTRPCClient<Router>({
    // links: [httpBatchLink({ transformer: SuperJSON, url: "http://localhost:3000/trpc" })],
    links: [
      splitLink({
        condition: (op) => op.path === "auth.isAuthenticated",
        // when condition is true, use normal request
        true: httpLink({ transformer: SuperJSON, url: "http://localhost:3000/trpc" }),
        // when condition is false, use batching
        false: httpBatchLink({ transformer: SuperJSON, url: "http://localhost:3000/trpc" }),
      }),
    ],
  });
}

export function TRPCProvider({ children }: { children: React.ReactNode }) {
  const queryClient = getQueryClient();
  const [trpcClient] = useState(() => getTRPCClient());

  return (
    <QueryClientProvider client={queryClient}>
      <RootTRPCProvider trpcClient={trpcClient} queryClient={queryClient}>
        {children}
      </RootTRPCProvider>
    </QueryClientProvider>
  );
}
