CREATE TABLE `proxy_logs` (
	`id` text PRIMARY KEY NOT NULL,
	`provider` text NOT NULL,
	`model` text NOT NULL,
	`modelFamilyId` text NOT NULL,
	`keyHash` text NOT NULL,
	`userId` text NOT NULL,
	`inputTokens` integer DEFAULT 0 NOT NULL,
	`outputTokens` integer DEFAULT 0 NOT NULL,
	`reasoningTokens` integer DEFAULT 0 NOT NULL,
	`createdAt` integer DEFAULT (strftime('%s', 'now')) NOT NULL,
	FOREIGN KEY (`modelFamilyId`) REFERENCES `proxy_modelFamilies`(`id`) ON UPDATE cascade ON DELETE cascade,
	FOR<PERSON><PERSON><PERSON> KEY (`keyHash`) REFERENCES `proxy_keys`(`hash`) ON UPDATE cascade ON DELETE cascade,
	FOREIGN KEY (`userId`) REFERENCES `proxy_users`(`id`) ON UPDATE cascade ON DELETE cascade
);
