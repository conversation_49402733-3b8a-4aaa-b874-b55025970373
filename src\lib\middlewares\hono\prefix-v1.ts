import { createMiddleware } from "hono/factory";

export function prefixV1() {
  return createMiddleware(async (ctx, next) => {
    const url = new URL(ctx.req.url);
    const [proxy, provider, ...pathParts] = url.pathname.split("/").filter(Boolean);

    // Check if the path matches the pattern /proxy/{provider}/* and is missing the /v1 or /v1beta prefix.
    if (
      proxy === "proxy" &&
      provider &&
      pathParts[0] &&
      !["v1", "v1beta", "v1alpha"].includes(pathParts[0]) &&
      pathParts.length > 0
    ) {
      switch (provider) {
        case "gemini":
          pathParts.splice(0, 0, "v1beta");
          break;

        default:
        case "openai":
          pathParts.splice(0, 0, "v1");
          break;
      }

      const newPathname = `/proxy/${provider}/` + pathParts.join("/");
      ctx.res = ctx.redirect(newPathname, 301);
    }

    return next();
  });
}
