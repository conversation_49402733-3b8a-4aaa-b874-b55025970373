import type { z } from "zod";

import type { Context } from "hono";
import { createFactory } from "hono/factory";
import { proxy } from "hono/proxy";

import { HttpError, PaymentRequiredError } from "@/shared/key-management/error";
import { type LLM_Providers } from "@/shared/providers";
import { tryCatch } from "@/shared/utils/try-catch";

import { createHeaders } from "./headers";

import { validateModelFamily } from "./request/validate-model-family";

import { handleBlockedResponse } from "./response/handlers/blocked-handler";
import { handleStreamedResponse } from "./response/handlers/streamed-handler";

import { logRequest } from "./response/log-request";
import { stripHeaders } from "./response/strip-headers";

export type Middleware = (input: { ctx: Context }) => Promise<void>;

export type Handler = (data: { ctx: Context; proxyResponse: Response }) => Promise<Response>;

const factory = createFactory();

export function createProxyHandler<Input extends z.ZodType, Output extends z.ZodType>(data: {
  provider: LLM_Providers;
  basePath: string;

  validateSchema: (ctx: Context) => Input;
  bodyTransform?: (ctx: Context, body: z.infer<Input>) => Promise<z.infer<Output>>;
  afterResponse?: Middleware[];
}) {
  const afterResponse = [stripHeaders, logRequest, ...(data.afterResponse ?? [])];

  const executeAfterResponse = factory.createMiddleware(async (ctx, next) => {
    await next();

    for (const middleware of afterResponse) {
      await middleware({ ctx });
    }

    async function executeAfterResponsePromises() {
      for (const promise of ctx.var.afterResponsePromises) {
        await (typeof promise === "function" ? promise() : promise);
      }
    }

    executeAfterResponsePromises().catch((err) => {
      ctx.var.logger.error(err, "Failed to execute after response promises");
    });
  });

  return factory.createHandlers(executeAfterResponse, async (ctx) => {
    const requestBody = await ctx.req.json();

    const requestData: Context["var"]["requestData"] = {
      model: requestBody.model,
      streaming: requestBody.stream,
      provider: data.provider,
      family: null,
    };

    if (data.provider === "gemini") {
      const [model, method] = ctx.req.param("path")!.split(":") as [string, string];
      requestData.model = model;
      requestData.streaming = method === "streamGenerateContent";
    }

    ctx.set("requestData", requestData);
    ctx.set("afterResponsePromises", []);

    const [family, err] = await tryCatch(() => validateModelFamily(ctx));
    if (err) {
      ctx.var.logger.warn({ err }, "Failed to validate model family");
      return Response.json({ error: { message: err.message } }, { status: 400 });
    }
    requestData.family = family;

    const { provider, basePath } = data;
    const parsedBody = data.validateSchema(ctx).safeParse(requestBody);

    if (!parsedBody.success) {
      ctx.var.logger.warn({ issues: parsedBody.error.issues }, "Invalid request");
      return Response.json(
        { error: { message: "Invalid request", issues: parsedBody.error.issues } },
        { status: 400 },
      );
    }

    let body: unknown = parsedBody.data;

    if (data.bodyTransform) {
      const [newBody, transformError] = await tryCatch(data.bodyTransform(ctx, parsedBody.data));

      if (transformError) {
        ctx.var.logger.error(transformError, "Failed to transform body");
        return Response.json(
          { error: { message: "Failed to transform body", error: transformError.message } },
          { status: 500 },
        );
      }

      body = newBody;
    }

    const forceSSE = provider === "gemini" && requestData.streaming ? "?alt=sse" : "";
    const url = `${basePath}${ctx.req.path.replace(`/proxy/${provider}`, "")}${forceSSE}`;

    const [key, keyError] = await tryCatch(
      ctx.var.keyPool.get(requestData.model, provider),
      PaymentRequiredError,
    );

    if (keyError) {
      ctx.var.logger.error(keyError, "Failed to get key");

      const status = keyError instanceof PaymentRequiredError ? 402 : 500;
      return Response.json({ error: { message: keyError.message } }, { status });
    }

    requestData.selectedKey = key;

    const headers = createHeaders({
      provider,
      headers: ctx.req.raw.headers,
      extra: { "Content-Type": "application/json" },
      key: requestData.selectedKey,
    });

    ctx.var.logger.info(
      { from: ctx.req.url, to: url, key: key.hash.slice(0, 10) + "..." },
      "Sending request to upstream API...",
    );

    const proxyResponse = await proxy(url, {
      method: ctx.req.method,
      body: JSON.stringify(parsedBody.data),
      headers,
    });

    ctx.var.logger.info(
      {
        status: proxyResponse.status,
        target: url,
        contentType: proxyResponse.headers.get("Content-Type"),
      },
      "Response received from upstream API",
    );

    const handler = requestData.streaming ? handleStreamedResponse : handleBlockedResponse;
    const [finalResponse, handlerError] = await tryCatch(
      handler({ ctx, proxyResponse }),
      HttpError,
    );

    if (handlerError) {
      ctx.var.logger.error(handlerError, "Failed to handle response");
      return Response.json(
        { error: { message: "Failed to handle response", error: handlerError.message } },
        { status: handlerError instanceof HttpError ? handlerError.code : 500 },
      );
    }

    return finalResponse;
  });
}
