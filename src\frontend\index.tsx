import "./styles/globals.css";

import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { createBrowserRouter, RouterProvider } from "react-router";

import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/sonner";

import { TRPCProvider } from "@/lib/trpc/client";

import { HomePage } from "./routes/home";

import { IndexAdminPage } from "./routes/admin";
import { AdminLayout } from "./routes/admin/_layout";
import { AdminDashboardPage } from "./routes/admin/dashboard";
import { AdminLoginPage } from "./routes/admin/login";
import { AdminLogsPage } from "./routes/admin/logs";
import { AdminModelsPage } from "./routes/admin/models";
import { AdminKeysPage } from "./routes/admin/keys";
import { AdminViewKeyPage } from "./routes/admin/keys/view-key";

const elem = document.getElementById("root")!;

const router = createBrowserRouter([
  { index: true, Component: HomePage },
  { path: "/admin/login", Component: AdminLoginPage },
  {
    path: "/admin",
    Component: AdminLayout,
    children: [
      { index: true, Component: IndexAdminPage },
      { path: "dashboard", Component: AdminDashboardPage },
      { path: "models", Component: AdminModelsPage },
      { path: "logs", Component: AdminLogsPage },
      {
        path: "keys",
        children: [
          { index: true, Component: AdminKeysPage },
          { path: ":hash", Component: AdminViewKeyPage },
        ],
      },
    ],
  },
]);

function Root() {
  return (
    <StrictMode>
      <ThemeProvider defaultTheme="dark" storageKey="ui-theme">
        <TRPCProvider>
          <RouterProvider router={router} />
        </TRPCProvider>

        <Toaster />
      </ThemeProvider>
    </StrictMode>
  );
}

const app = <Root />;

if (import.meta.hot) {
  const root = (import.meta.hot.data.root ??= createRoot(elem));
  root.render(app);
} else {
  createRoot(elem).render(app);
}
