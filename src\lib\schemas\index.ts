import type { DeepSeekChatCompletionResponse } from "@/lib/schemas/output-deepseek";
import type { GeminiGenerateContentResponse } from "@/lib/schemas/output-gemini";
import type { OpenAIChatCompletionResponse } from "@/lib/schemas/output-openai-chat";

import type { DeepSeekChatCompletionStreamResponse } from "@/lib/schemas/output-deepseek";
import type { GeminiStreamGenerateContentResponse } from "@/lib/schemas/output-gemini";
import type { OpenAIChatCompletionStreamResponse } from "@/lib/schemas/output-openai-chat";

type ResponseChunk =
  | GeminiStreamGenerateContentResponse
  | DeepSeekChatCompletionStreamResponse
  | OpenAIChatCompletionStreamResponse;

type ResponseBody =
  | GeminiGenerateContentResponse
  | DeepSeekChatCompletionResponse
  | OpenAIChatCompletionResponse;

export type {
  ResponseBody,
  DeepSeekChatCompletionResponse,
  GeminiGenerateContentResponse,
  OpenAIChatCompletionResponse,
  ResponseChunk,
  DeepSeekChatCompletionStreamResponse,
  GeminiStreamGenerateContentResponse,
  OpenAIChatCompletionStreamResponse,
};
