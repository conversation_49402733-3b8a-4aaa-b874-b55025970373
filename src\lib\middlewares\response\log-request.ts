import { schema } from "@/shared/database";

import type { Middleware } from "../create-proxy";

export const logRequest: Middleware = async ({ ctx }) => {
  const requestData = ctx.var.requestData!;
  let usages = { inputTokens: 0, outputTokens: 0, reasoningTokens: 0 };

  if (!ctx.res.ok) return;

  async function startLogRequest() {
    const body = ctx.var.fullResponseBody;
    if (!body) return;

    switch (true) {
      case requestData.provider === "gemini" && "usageMetadata" in body: {
        usages = {
          inputTokens: body.usageMetadata.promptTokenCount,
          outputTokens: body.usageMetadata.candidatesTokenCount,
          reasoningTokens: body.usageMetadata.thoughtsTokenCount ?? 0,
        };
        break;
      }

      case requestData.provider === "deepseek" && "usage" in body: {
        usages = {
          inputTokens: body.usage?.prompt_tokens ?? 0,
          outputTokens: body.usage?.completion_tokens ?? 0,
          reasoningTokens: body.usage?.completion_tokens_details?.reasoning_tokens ?? 0,
        };
        break;
      }

      case requestData.provider === "openai" && "usage" in body: {
        usages = {
          inputTokens: body.usage?.prompt_tokens ?? 0,
          outputTokens: body.usage?.completion_tokens ?? 0,
          reasoningTokens: body.usage?.completion_tokens_details?.reasoning_tokens ?? 0,
        };
        break;
      }
    }

    await ctx.var.db.insert(schema.logs).values({
      provider: requestData.provider,
      model: requestData.model,
      modelFamilyId: requestData.family!.id,
      keyHash: requestData.selectedKey!.hash,
      userId: "univnrdhsfj5r3yin9co4kul",

      inputTokens: usages.inputTokens,
      outputTokens: usages.outputTokens,
      reasoningTokens: usages.reasoningTokens,
    });
  }

  ctx.var.afterResponsePromises.push(startLogRequest);
};
