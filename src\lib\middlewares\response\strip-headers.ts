import type { Middleware } from "../create-proxy";

export const stripHeaders: Middleware = async ({ ctx }) => {
  ctx.res.headers.delete("x-api-key");
  ctx.res.headers.delete("x-goog-api-key");

  ctx.res.headers.delete("set-cookie");

  ctx.res.headers.delete("Cf-Cache-Status");
  ctx.res.headers.delete("Cf-Ray");

  ctx.res.headers.delete("Openai-Organization");
  ctx.res.headers.delete("Openai-Processing-Ms");
  ctx.res.headers.delete("Openai-Project");
  ctx.res.headers.delete("Openai-Version");

  ctx.res.headers.delete("Vary");
  ctx.res.headers.delete("Server");
  ctx.res.headers.delete("Alt-Svc");
  ctx.res.headers.delete("Server-Timing");
  ctx.res.headers.delete("Strict-Transport-Security");
  ctx.res.headers.delete("Access-Control-Expose-Headers");

  ctx.res.headers.delete("X-Ratelimit-Limit-Requests");
  ctx.res.headers.delete("X-Ratelimit-Limit-Tokens");
  ctx.res.headers.delete("X-Ratelimit-Remaining-Requests");
  ctx.res.headers.delete("X-Ratelimit-Remaining-Tokens");
  ctx.res.headers.delete("X-Ratelimit-Reset-Requests");
  ctx.res.headers.delete("X-Ratelimit-Reset-Tokens");

  ctx.res.headers.delete("X-Request-Id");
  ctx.res.headers.delete("X-Frame-Options");
  ctx.res.headers.delete("X-Xss-Protection");
  ctx.res.headers.delete("X-Content-Type-Options");
  ctx.res.headers.delete("X-Envoy-Upstream-Service-Time");
};
