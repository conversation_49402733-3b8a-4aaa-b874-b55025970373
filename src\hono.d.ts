import type pino from "pino";

import type { ResponseBody } from "./lib/schemas";

import type { db } from "./shared/database";
import type { modelFamilies } from "./shared/database/schema/modelFamilies";
import type { Key } from "./shared/key-management";
import type { KeysPool } from "./shared/key-management/keys-pool";
import type { LLM_Providers } from "./shared/providers";

declare module "hono" {
  interface ContextVariableMap {
    token: string;
    logger: pino.Logger;
    keyPool: KeysPool;
    db: typeof db;

    requestData?: {
      model: string;
      streaming: boolean;
      provider: LLM_Providers;

      selectedKey?: Key;
      family: (typeof modelFamilies)["$inferSelect"] | null;
    };

    afterResponsePromises: (Promise<unknown> | (() => Promise<unknown>))[];
    fullResponseBody: ResponseBody;
  }
}
