import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { createUpdateSchema } from "drizzle-zod";
import { SearchIcon, XIcon } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Icons } from "@/components/ui/icons";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { modelFamilies } from "@/shared/database/schema/modelFamilies";
import { LLM_PROVIDERS, LLM_PROVIDER_DISPLAY_NAME, type LLM_Providers } from "@/shared/providers";
import { cn, format } from "@/shared/utils";

import { useTRPC } from "@/lib/trpc/client";

const modelFamilySchema = createUpdateSchema(modelFamilies, {
  provider: z.enum(LLM_PROVIDERS),
});

type ModelFamilyForm = z.infer<typeof modelFamilySchema>;

type ModelFamily = typeof modelFamilies.$inferSelect;

type UpdateModelFamilyDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  family: ModelFamily;
};

export function UpdateModelFamilyDialog({
  open,
  onOpenChange,
  family,
}: UpdateModelFamilyDialogProps) {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<ModelFamilyForm>({
    resolver: zodResolver(modelFamilySchema),
    defaultValues: {
      name: family.name,
      provider: family.provider as LLM_Providers,
      models: family.models,
      status: family.status,
      inputCost: family.inputCost,
      outputCost: family.outputCost,
      maxContext: family.maxContext,
      maxOutput: family.maxOutput,
      ratelimitCost: family.ratelimitCost,
    },
    values: {
      name: family.name,
      provider: family.provider as LLM_Providers,
      models: family.models,
      status: family.status,
      inputCost: family.inputCost,
      outputCost: family.outputCost,
      maxContext: family.maxContext,
      maxOutput: family.maxOutput,
      ratelimitCost: family.ratelimitCost,
    },
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [modelSearchQuery, setModelSearchQuery] = useState("");
  const selectedProvider = watch("provider");
  const selectedModels = watch("models") || [];

  const { data: modelIds } = useQuery(
    trpc.models.getModelIdsForProvider.queryOptions(
      { provider: selectedProvider },
      { enabled: !!selectedProvider },
    ),
  );

  const availableModels = (modelIds || []).filter(
    (modelId) =>
      !selectedModels.includes(modelId) &&
      modelId.toLowerCase().includes(modelSearchQuery.toLowerCase()),
  );

  const { mutateAsync: updateModelFamily } = useMutation(trpc.models.update.mutationOptions());

  function addModel(modelId: string) {
    const currentModels = selectedModels;
    if (!currentModels.includes(modelId)) {
      setValue("models", [...currentModels, modelId]);
      setModelSearchQuery("");
    }
  }

  function removeModel(modelId: string) {
    const currentModels = selectedModels;
    setValue(
      "models",
      currentModels.filter((id) => id !== modelId),
    );
  }

  async function onSubmit(data: ModelFamilyForm) {
    setIsSubmitting(true);
    try {
      await updateModelFamily({ id: family.id, ...data });
      queryClient.invalidateQueries({ queryKey: trpc.models.list.queryKey() });
      onOpenChange(false);
    } catch (error) {
      console.error("Failed to update model family:", error);
    } finally {
      setIsSubmitting(false);
    }
  }

  function handleClose() {
    reset();
    setModelSearchQuery("");
    onOpenChange(false);
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="!max-w-6xl">
        <DialogHeader>
          <DialogTitle>Edit Model Family</DialogTitle>
        </DialogHeader>

        <form
          onSubmit={handleSubmit(onSubmit)}
          className="grid grid-rows-1 gap-4 md:grid-cols-2 md:gap-6"
        >
          <div className="col-span-2">
            <div className="space-y-6">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="name">Family Name</Label>
                  <Input
                    id="name"
                    placeholder="e.g: GPT-4 Turbo"
                    {...register("name")}
                    className={errors.name ? "border-red-500" : ""}
                  />
                  {errors.name && <p className="text-sm text-red-500">{errors.name.message}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="provider">Provider</Label>
                  <Select
                    value={selectedProvider}
                    onValueChange={(value) => {
                      setValue("provider", value as LLM_Providers);
                      setValue("models", []);
                      setModelSearchQuery("");
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Provider" />
                    </SelectTrigger>

                    <SelectContent>
                      {LLM_PROVIDERS.map((provider) => (
                        <SelectItem key={provider} value={provider}>
                          <div className="flex items-center gap-2">
                            <Icons.provider provider={provider} className="size-4" />
                            <span>{LLM_PROVIDER_DISPLAY_NAME[provider]}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.provider && (
                    <p className="text-sm text-red-500">{errors.provider.message}</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={watch("status")}
                  onValueChange={(value: "enabled" | "disabled") => setValue("status", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="disabled">
                      <div className="flex items-center gap-2">
                        <div className="h-2 w-2 rounded-full bg-red-500" />
                        <span>Disabled</span>
                      </div>
                    </SelectItem>

                    <SelectItem value="enabled">
                      <div className="flex items-center gap-2">
                        <div className="h-2 w-2 rounded-full bg-green-500" />
                        <span>Enabled</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="ratelimitCost">Rate Limit Cost</Label>
                <Input
                  id="ratelimitCost"
                  type="number"
                  min="1"
                  step="1"
                  {...register("ratelimitCost", { valueAsNumber: true })}
                  className={errors.ratelimitCost ? "border-red-500" : ""}
                />
                {errors.ratelimitCost && (
                  <p className="text-sm text-red-500">{errors.ratelimitCost.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Pricing Configuration</h3>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="inputCost" className="min-w-max">
                    Input Cost (per 1M tokens)
                  </Label>
                  <div className="relative">
                    <Input
                      id="inputCost"
                      type="number"
                      min="0"
                      step="0.01"
                      placeholder="0.00"
                      {...register("inputCost", { valueAsNumber: true })}
                      className={cn(errors.inputCost ? "border-red-500" : "", "appearance-none")}
                    />
                    <div className="text-muted-foreground absolute top-1/2 right-3 -translate-y-1/2 transform text-sm">
                      {format.cost(watch("inputCost") || 0)}
                    </div>
                  </div>
                  {errors.inputCost && (
                    <p className="text-sm text-red-500">{errors.inputCost.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="outputCost" className="min-w-max">
                    Output Cost (per 1M tokens)
                  </Label>
                  <div className="relative">
                    <Input
                      id="outputCost"
                      type="number"
                      min="0"
                      step="0.01"
                      placeholder="0.00"
                      {...register("outputCost", { valueAsNumber: true })}
                      className={errors.outputCost ? "border-red-500" : ""}
                    />
                    <div className="text-muted-foreground absolute top-1/2 right-3 -translate-y-1/2 transform text-sm">
                      {format.cost(watch("outputCost") || 0)}
                    </div>
                  </div>
                  {errors.outputCost && (
                    <p className="text-sm text-red-500">{errors.outputCost.message}</p>
                  )}
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Token Limits</h3>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="maxContext">Max Context Tokens</Label>
                  <Input
                    id="maxContext"
                    type="number"
                    min="0"
                    step="1"
                    placeholder="0"
                    {...register("maxContext", { valueAsNumber: true })}
                    className={errors.maxContext ? "border-red-500" : ""}
                  />
                  {errors.maxContext && (
                    <p className="text-sm text-red-500">{errors.maxContext.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxOutput">Max Output Tokens</Label>
                  <Input
                    id="maxOutput"
                    type="number"
                    min="0"
                    step="1"
                    placeholder="0"
                    {...register("maxOutput", { valueAsNumber: true })}
                    className={errors.maxOutput ? "border-red-500" : ""}
                  />
                  {errors.maxOutput && (
                    <p className="text-sm text-red-500">{errors.maxOutput.message}</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-medium">Model Configuration</h3>
            <p className="text-muted-foreground text-sm">
              Select the specific model IDs that belong to this family. At least one model is
              required.
            </p>

            <div className="space-y-3">
              <Label htmlFor="modelSearch">Available Models</Label>
              <div className="relative">
                <SearchIcon className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform" />
                <Input
                  id="modelSearch"
                  placeholder={
                    selectedProvider
                      ? `Search ${LLM_PROVIDER_DISPLAY_NAME[selectedProvider]} models...`
                      : "Select a provider first"
                  }
                  value={modelSearchQuery}
                  onChange={(e) => setModelSearchQuery(e.target.value)}
                  disabled={!selectedProvider || !modelIds?.length}
                  className="pl-10"
                />
              </div>

              {selectedProvider && availableModels.length > 0 && modelSearchQuery && (
                <div className="bg-background max-h-40 overflow-y-auto rounded-md border">
                  {availableModels.slice(0, 10).map((modelId) => (
                    <button
                      key={modelId}
                      type="button"
                      onClick={() => addModel(modelId)}
                      className="hover:bg-accent hover:text-accent-foreground w-full border-b px-3 py-2 text-left text-sm last:border-b-0"
                    >
                      {modelId}
                    </button>
                  ))}

                  {availableModels.length > 10 && (
                    <div className="text-muted-foreground border-t px-3 py-2 text-xs">
                      Showing first 10 results. Refine your search for more specific results.
                    </div>
                  )}
                </div>
              )}

              {selectedProvider && modelIds?.length === 0 && (
                <p className="text-muted-foreground text-sm">
                  No models available for {LLM_PROVIDER_DISPLAY_NAME[selectedProvider]}
                </p>
              )}

              {selectedModels.length > 0 && (
                <div className="space-y-2">
                  <Label>Selected Models ({selectedModels.length})</Label>
                  <div className="flex flex-wrap gap-2">
                    {selectedModels.map((modelId) => (
                      <Badge
                        key={modelId}
                        variant="secondary"
                        className="flex items-center gap-1 rounded-md px-4 py-2"
                      >
                        <span className="text-xs">{modelId}</span>
                        <button
                          type="button"
                          onClick={() => removeModel(modelId)}
                          className="hover:bg-destructive hover:text-destructive-foreground ml-1 rounded-md p-0.5 transition-colors"
                        >
                          <XIcon className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {errors.models && <p className="text-sm text-red-500">{errors.models.message}</p>}
            </div>
          </div>

          <DialogFooter className="col-span-2">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>

            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Saving..." : "Save Changes"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
