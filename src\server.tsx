import { trpcServer } from "@hono/trpc-server";
import { Hono } from "hono";
import { cors } from "hono/cors";
import { secureHeaders } from "hono/secure-headers";

import index from "@/frontend/index.html";

import { loggerMiddleware } from "./lib/middlewares/hono/logger";
import { trimTrailingSlash } from "./lib/middlewares/hono/trim-trailing";

import { db } from "./shared/database";
import { migrateDatabase } from "./shared/database/migrator";
import { keyCleanupService } from "./shared/key-management/cleanup";
import { keysPool } from "./shared/key-management/keys-pool";

import { createContext } from "./lib/trpc";
import { router } from "./lib/trpc/root";

import { proxyRoute } from "./proxy/route";

import { config } from "./config";
import { logger } from "./logger";

const app = new Hono()
  .use(trimTrailingSlash())
  .use(cors({ origin: "*", allowMethods: ["GET", "POST", "OPTIONS"] }))
  .use(secureHeaders({ removePoweredBy: true }))
  .use(loggerMiddleware())
  .use((ctx, next) => {
    ctx.set("logger", logger.child({ module: "server" }));
    ctx.set("keyPool", keysPool);
    ctx.set("db", db);
    return next();
  })

  .use("/trpc/*", trpcServer({ router, createContext }));

app.route(config.proxyEndpointRoute, proxyRoute);

const server = Bun.serve({
  routes: {
    "/": index,
    "/admin": index,
    "/admin/*": index,
  },

  fetch: app.fetch,
  development: process.env.NODE_ENV !== "production" && { hmr: true, console: true },
});

await migrateDatabase();

logger.info(`🚀 Server running at ${server.url} - Env: ${config.enviroment}`);
await keysPool.init();
keyCleanupService.start();
