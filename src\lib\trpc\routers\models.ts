import { eq } from "drizzle-orm";
import { nanoid } from "nanoid";
import z from "zod";

import { protectedProcedure, t } from "..";

import { schema } from "@/shared/database";
import { LLM_PROVIDERS } from "@/shared/providers";
import { createInsertSchema } from "drizzle-zod";

const createModelFamilySchema = createInsertSchema(schema.modelFamilies).omit({ id: true });
const updateModelFamilySchema = createModelFamilySchema.partial().extend({ id: z.string() });

export const modelsRouter = t.router({
  list: protectedProcedure.query(async ({ ctx }) => {
    return await ctx.db.query.modelFamilies.findMany({
      orderBy: (table, { desc }) => [desc(table.createdAt)],
    });
  }),

  create: protectedProcedure.input(createModelFamilySchema).mutation(async ({ ctx, input }) => {
    const id = nanoid();

    await ctx.db.insert(schema.modelFamilies).values({ id, ...input });
    return { id };
  }),

  update: protectedProcedure.input(updateModelFamilySchema).mutation(async ({ ctx, input }) => {
    const { id, ...updateData } = input;

    await ctx.db
      .update(schema.modelFamilies)
      .set(updateData)
      .where(eq(schema.modelFamilies.id, id));

    return { success: true };
  }),

  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.db.delete(schema.modelFamilies).where(eq(schema.modelFamilies.id, input.id));

      return { success: true };
    }),

  getModelIdsForProvider: protectedProcedure
    .input(z.object({ provider: z.enum(LLM_PROVIDERS) }))
    .query(async ({ ctx, input }) => {
      const families = await ctx.db.query.keys.findMany({
        where: (table, { eq }) => eq(table.provider, input.provider),
      });

      return Array.from(new Set(families.map((f) => f.modelIds).flat()));
    }),
});
