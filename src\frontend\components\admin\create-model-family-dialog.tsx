import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { createInsertSchema } from "drizzle-zod";
import { SearchIcon, XIcon } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Icons } from "@/components/ui/icons";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { modelFamilies } from "@/shared/database/schema/modelFamilies";
import { LLM_PROVIDERS, LLM_PROVIDER_DISPLAY_NAME, type LLM_Providers } from "@/shared/providers";
import { cn } from "@/shared/utils";

import { useTRPC } from "@/lib/trpc/client";

const createModelFamilySchema = createInsertSchema(modelFamilies).omit({ id: true });

type CreateModelFamilyForm = z.infer<typeof createModelFamilySchema>;

type CreateModelFamilyDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

export function CreateModelFamilyDialog({ open, onOpenChange }: CreateModelFamilyDialogProps) {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<CreateModelFamilyForm>({
    resolver: zodResolver(createModelFamilySchema),
    defaultValues: {
      status: "disabled",
      inputCost: 0,
      outputCost: 0,
      maxContext: 0,
      maxOutput: 0,
      ratelimitCost: 1,
      models: [],
    },
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [modelSearchQuery, setModelSearchQuery] = useState("");
  const selectedProvider = watch("provider");
  const selectedModels = watch("models") || [];

  const { data: modelIds } = useQuery(
    trpc.models.getModelIdsForProvider.queryOptions(
      { provider: selectedProvider },
      { enabled: !!selectedProvider },
    ),
  );

  // Filter available models based on search query and exclude already selected ones
  const availableModels = (modelIds || []).filter(
    (modelId) =>
      !selectedModels.includes(modelId) &&
      modelId.toLowerCase().includes(modelSearchQuery.toLowerCase()),
  );
  const { mutateAsync: createModelFamily } = useMutation(trpc.models.create.mutationOptions());

  // Helper functions for model management
  const addModel = (modelId: string) => {
    const currentModels = selectedModels;
    if (!currentModels.includes(modelId)) {
      setValue("models", [...currentModels, modelId]);
      setModelSearchQuery(""); // Clear search after adding
    }
  };

  const removeModel = (modelId: string) => {
    const currentModels = selectedModels;
    setValue(
      "models",
      currentModels.filter((id) => id !== modelId),
    );
  };

  const onSubmit = async (data: CreateModelFamilyForm) => {
    setIsSubmitting(true);
    try {
      await createModelFamily(data);
      queryClient.invalidateQueries({ queryKey: [["models", "list"]] });
      reset();
      setModelSearchQuery("");
      onOpenChange(false);
    } catch (error) {
      console.error("Failed to create model family:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    reset();
    setModelSearchQuery("");
    onOpenChange(false);
  };

  const formatCost = (cost: number) => {
    return `$${cost} / 1M`;
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="!max-w-6xl">
        <DialogHeader>
          <DialogTitle>Create New Model Family</DialogTitle>
        </DialogHeader>

        <form
          onSubmit={handleSubmit(onSubmit)}
          className="grid grid-rows-1 gap-4 md:grid-cols-2 md:gap-6"
        >
          <div className="col-span-2">
            <div className="space-y-6">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {/* Name */}
                <div className="space-y-2">
                  <Label htmlFor="name">Family Name</Label>
                  <Input
                    id="name"
                    placeholder="e.g: GPT-4 Turbo"
                    {...register("name")}
                    className={errors.name ? "border-red-500" : ""}
                  />
                  {errors.name && <p className="text-sm text-red-500">{errors.name.message}</p>}
                </div>

                {/* Provider */}
                <div className="space-y-2">
                  <Label htmlFor="provider">Provider</Label>
                  <Select
                    value={selectedProvider}
                    onValueChange={(value) => setValue("provider", value as LLM_Providers)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Provider" />
                    </SelectTrigger>

                    <SelectContent>
                      {LLM_PROVIDERS.map((provider) => (
                        <SelectItem key={provider} value={provider}>
                          <div className="flex items-center gap-2">
                            <Icons.provider provider={provider} className="size-4" />
                            <span>{LLM_PROVIDER_DISPLAY_NAME[provider]}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.provider && (
                    <p className="text-sm text-red-500">{errors.provider.message}</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {/* Status */}
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={watch("status")}
                  onValueChange={(value: "enabled" | "disabled") => setValue("status", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>

                  <SelectContent>
                    <SelectItem value="disabled">
                      <div className="flex items-center gap-2">
                        <div className="h-2 w-2 rounded-full bg-red-500" />
                        <span>Disabled</span>
                      </div>
                    </SelectItem>

                    <SelectItem value="enabled">
                      <div className="flex items-center gap-2">
                        <div className="h-2 w-2 rounded-full bg-green-500" />
                        <span>Enabled</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Rate Limit Cost */}
              <div className="space-y-2">
                <Label htmlFor="ratelimitCost">Rate Limit Cost</Label>
                <Input
                  id="ratelimitCost"
                  type="number"
                  min="1"
                  step="1"
                  {...register("ratelimitCost", { valueAsNumber: true })}
                  className={errors.ratelimitCost ? "border-red-500" : ""}
                />
                {errors.ratelimitCost && (
                  <p className="text-sm text-red-500">{errors.ratelimitCost.message}</p>
                )}
              </div>
            </div>

            {/* Cost Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Pricing Configuration</h3>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {/* Input Cost */}
                <div className="space-y-2">
                  <Label htmlFor="inputCost" className="min-w-max">
                    Input Cost (per 1M tokens)
                  </Label>

                  <div className="relative">
                    <Input
                      id="inputCost"
                      type="number"
                      min="0"
                      step="0.01"
                      placeholder="0.00"
                      {...register("inputCost", { valueAsNumber: true })}
                      className={cn(errors.inputCost ? "border-red-500" : "", "appearance-none")}
                    />
                    <div className="text-muted-foreground absolute top-1/2 right-3 -translate-y-1/2 transform text-sm">
                      {formatCost(watch("inputCost") || 0)}
                    </div>
                  </div>

                  {errors.inputCost && (
                    <p className="text-sm text-red-500">{errors.inputCost.message}</p>
                  )}
                </div>

                {/* Output Cost */}
                <div className="space-y-2">
                  <Label htmlFor="outputCost" className="min-w-max">
                    Output Cost (per 1M tokens)
                  </Label>

                  <div className="relative">
                    <Input
                      id="outputCost"
                      type="number"
                      min="0"
                      step="0.01"
                      placeholder="0.00"
                      {...register("outputCost", { valueAsNumber: true })}
                      className={errors.outputCost ? "border-red-500" : ""}
                    />

                    <div className="text-muted-foreground absolute top-1/2 right-3 -translate-y-1/2 transform text-sm">
                      {formatCost(watch("outputCost") || 0)}
                    </div>
                  </div>

                  {errors.outputCost && (
                    <p className="text-sm text-red-500">{errors.outputCost.message}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Limits Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Token Limits</h3>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {/* Max Context */}
                <div className="space-y-2">
                  <Label htmlFor="maxContext">Max Context Tokens</Label>
                  <Input
                    id="maxContext"
                    type="number"
                    min="0"
                    step="1"
                    placeholder="0"
                    {...register("maxContext", { valueAsNumber: true })}
                    className={errors.maxContext ? "border-red-500" : ""}
                  />
                  {errors.maxContext && (
                    <p className="text-sm text-red-500">{errors.maxContext.message}</p>
                  )}
                </div>

                {/* Max Output */}
                <div className="space-y-2">
                  <Label htmlFor="maxOutput">Max Output Tokens</Label>
                  <Input
                    id="maxOutput"
                    type="number"
                    min="0"
                    step="1"
                    placeholder="0"
                    {...register("maxOutput", { valueAsNumber: true })}
                    className={errors.maxOutput ? "border-red-500" : ""}
                  />

                  {errors.maxOutput && (
                    <p className="text-sm text-red-500">{errors.maxOutput.message}</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Model Configuration Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Model Configuration</h3>
            <p className="text-muted-foreground text-sm">
              Select the specific model IDs that belong to this family. At least one model is
              required.
            </p>

            {/* Model Search and Selection */}
            <div className="space-y-3">
              <Label htmlFor="modelSearch">Available Models</Label>
              <div className="relative">
                <SearchIcon className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform" />
                <Input
                  id="modelSearch"
                  placeholder={
                    selectedProvider
                      ? `Search ${LLM_PROVIDER_DISPLAY_NAME[selectedProvider]} models...`
                      : "Select a provider first"
                  }
                  value={modelSearchQuery}
                  onChange={(e) => setModelSearchQuery(e.target.value)}
                  disabled={!selectedProvider || !modelIds?.length}
                  className="pl-10"
                />
              </div>

              {/* Available Models Dropdown */}
              {selectedProvider && availableModels.length > 0 && modelSearchQuery && (
                <div className="bg-background max-h-40 overflow-y-auto rounded-md border">
                  {availableModels.slice(0, 10).map((modelId) => (
                    <button
                      key={modelId}
                      type="button"
                      onClick={() => addModel(modelId)}
                      className="hover:bg-accent hover:text-accent-foreground w-full border-b px-3 py-2 text-left text-sm last:border-b-0"
                    >
                      {modelId}
                    </button>
                  ))}

                  {availableModels.length > 10 && (
                    <div className="text-muted-foreground border-t px-3 py-2 text-xs">
                      Showing first 10 results. Refine your search for more specific results.
                    </div>
                  )}
                </div>
              )}

              {/* No models message */}
              {selectedProvider && modelIds?.length === 0 && (
                <p className="text-muted-foreground text-sm">
                  No models available for {LLM_PROVIDER_DISPLAY_NAME[selectedProvider]}
                </p>
              )}

              {/* Selected Models Display */}
              {selectedModels.length > 0 && (
                <div className="space-y-2">
                  <Label>Selected Models ({selectedModels.length})</Label>
                  <div className="flex flex-wrap gap-2">
                    {selectedModels.map((modelId) => (
                      <Badge
                        key={modelId}
                        variant="secondary"
                        className="flex items-center gap-1 rounded-md px-4 py-2"
                      >
                        <span className="text-xs">{modelId}</span>

                        <button
                          type="button"
                          onClick={() => removeModel(modelId)}
                          className="hover:bg-destructive hover:text-destructive-foreground ml-1 rounded-md p-0.5 transition-colors"
                        >
                          <XIcon className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Validation Error */}
              {errors.models && <p className="text-sm text-red-500">{errors.models.message}</p>}
            </div>
          </div>

          <DialogFooter className="col-span-2">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>

            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Creating..." : "Create Family"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
