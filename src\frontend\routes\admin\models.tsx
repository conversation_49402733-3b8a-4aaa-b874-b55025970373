import { useQuery } from "@tanstack/react-query";
import {
  ArrowDownAZIcon,
  ArrowUpAZIcon,
  CalendarArrowUpIcon,
  ClockArrowDownIcon,
  ClockArrowUpIcon,
  PlusIcon,
  SearchIcon,
} from "lucide-react";
import { useState } from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Icons } from "@/components/ui/icons";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { CreateModelFamilyDialog } from "@/components/admin/create-model-family-dialog";
import { ModelFamilyCard } from "@/components/admin/model-family-card";

import { useTRPC } from "@/lib/trpc/client";
import { LLM_PROVIDERS, LLM_PROVIDER_DISPLAY_NAME, type LLM_Providers } from "@/shared/providers";
import { format } from "@/shared/utils";

type ModelFamily = {
  id: string;
  name: string;
  provider: LLM_Providers;
  models: string[];
  status: "enabled" | "disabled";
  inputCost: number;
  outputCost: number;
  maxContext: number;
  maxOutput: number;
  ratelimitCost: number;
  createdAt: number;
  updatedAt: number;
};

type ModelFamilyStatus = "enabled" | "disabled" | "all";
type SortOption = "a-z" | "z-a" | "oldest" | "newest" | "updated";

export function AdminModelsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedProvider, setSelectedProvider] = useState<LLM_Providers | "all">("all");
  const [selectedStatus, setSelectedStatus] = useState<ModelFamilyStatus>("all");
  const [sortBy, setSortBy] = useState<SortOption>("a-z");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  const trpc = useTRPC();
  const { data: modelFamilies, isLoading } = useQuery(trpc.models.list.queryOptions());

  // Get most used model from logs
  const { data: mostUsedModel } = useQuery(trpc.logs.getMostUsedModel.queryOptions());

  // Filter and sort model families based on search, filters, and sort option
  const filteredAndSortedFamilies =
    modelFamilies
      ?.filter((family: ModelFamily) => {
        const matchesSearch = family.name.toLowerCase().includes(searchQuery.toLowerCase());
        const matchesProvider = selectedProvider === "all" || family.provider === selectedProvider;
        const matchesStatus = selectedStatus === "all" || family.status === selectedStatus;

        return matchesSearch && matchesProvider && matchesStatus;
      })
      .sort((a: ModelFamily, b: ModelFamily) => {
        switch (sortBy) {
          case "a-z":
            return a.name.localeCompare(b.name);
          case "z-a":
            return b.name.localeCompare(a.name);
          case "oldest":
            return a.createdAt - b.createdAt;
          case "newest":
            return b.createdAt - a.createdAt;
          case "updated":
            return b.updatedAt - a.updatedAt;
          default:
            return 0;
        }
      }) ?? [];

  // Statistics
  const totalFamilies = modelFamilies?.length || 0;
  const enabledFamilies =
    modelFamilies?.filter((f: ModelFamily) => f.status === "enabled").length || 0;

  return (
    <div className="@container flex flex-col gap-2 p-2">
      <div className="grid grid-cols-1 gap-2 md:grid-cols-4">
        <Card className="gap-2 py-4">
          <CardHeader className="flex flex-row items-center justify-between pb-0">
            <CardTitle className="text-lg font-medium">Total Families</CardTitle>
            <Icons.logo className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalFamilies}</div>
            <p className="text-muted-foreground text-xs">Across all providers</p>
          </CardContent>
        </Card>

        <Card className="gap-2 py-4">
          <CardHeader className="flex flex-row items-center justify-between pb-0">
            <CardTitle className="text-lg font-medium">Enabled Families</CardTitle>
            <div className="h-4 w-4 rounded-full bg-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{enabledFamilies}</div>
            <p className="text-muted-foreground text-xs">Available to users</p>
          </CardContent>
        </Card>

        <Card className="gap-2 py-4">
          <CardHeader className="flex flex-row items-center justify-between pb-0">
            <CardTitle className="text-lg font-medium">Most Used</CardTitle>
            <div className="h-4 w-4 rounded-full bg-blue-500" />
          </CardHeader>
          <CardContent>
            {mostUsedModel ? (
              <>
                <div className="text-2xl font-bold">{mostUsedModel.modelFamilyName}</div>
                <div className="text-muted-foreground flex items-center gap-2 text-xs">
                  <Icons.provider provider={mostUsedModel.provider} className="h-3 w-3" />
                  <span>{format.number(mostUsedModel.totalRequests)} requests</span>
                  <span>•</span>
                  <span>{format.number(mostUsedModel.totalTokens)} tokens</span>
                </div>
              </>
            ) : (
              <>
                <div className="text-2xl font-bold">N/A</div>
                <p className="text-muted-foreground text-xs">0 requests</p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="flex flex-col items-center gap-2">
        <div className="relative w-full flex-1">
          <SearchIcon className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform" />
          <Input
            placeholder="Search families by name..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="flex w-full items-center justify-between gap-2">
          <div className="flex gap-2">
            {/* Provider Filter */}
            <Select
              value={selectedProvider}
              onValueChange={(value: LLM_Providers | "all") => setSelectedProvider(value)}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Provider" />
              </SelectTrigger>

              <SelectContent>
                <SelectItem value="all">All Providers</SelectItem>

                {LLM_PROVIDERS.map((provider) => (
                  <SelectItem key={provider} value={provider}>
                    <div className="flex items-center gap-2">
                      <Icons.provider provider={provider} className="size-4" />
                      <span>{LLM_PROVIDER_DISPLAY_NAME[provider]}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select
              value={selectedStatus}
              onValueChange={(value: "enabled" | "disabled" | "all") => setSelectedStatus(value)}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>

              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="enabled">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-green-500" />
                    <span>Enabled</span>
                  </div>
                </SelectItem>
                <SelectItem value="disabled">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-red-500" />
                    <span>Disabled</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>

            {/* Sort Select */}
            <Select value={sortBy} onValueChange={(value: SortOption) => setSortBy(value)}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>

              <SelectContent>
                <SelectItem value="a-z">
                  <div className="flex items-center gap-2">
                    <ArrowDownAZIcon className="h-4 w-4" />
                    <span>A-Z</span>
                  </div>
                </SelectItem>
                <SelectItem value="z-a">
                  <div className="flex items-center gap-2">
                    <ArrowUpAZIcon className="h-4 w-4" />
                    <span>Z-A</span>
                  </div>
                </SelectItem>
                <SelectItem value="oldest">
                  <div className="flex items-center gap-2">
                    <ClockArrowDownIcon className="h-4 w-4" />
                    <span>Oldest</span>
                  </div>
                </SelectItem>
                <SelectItem value="newest">
                  <div className="flex items-center gap-2">
                    <ClockArrowUpIcon className="h-4 w-4" />
                    <span>Newest</span>
                  </div>
                </SelectItem>
                <SelectItem value="updated">
                  <div className="flex items-center gap-2">
                    <CalendarArrowUpIcon className="h-4 w-4" />
                    <span>Updated</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>

            <Button
              data-show={
                searchQuery.length > 0 ||
                selectedProvider !== "all" ||
                selectedStatus !== "all" ||
                sortBy !== "a-z"
              }
              onClick={() => {
                setSearchQuery("");
                setSelectedProvider("all");
                setSelectedStatus("all");
                setSortBy("a-z");
              }}
              className="hidden data-[show=true]:block"
            >
              Clear
            </Button>
          </div>

          {/* Create Button */}
          <Button
            title="Create New Model Family"
            onClick={() => setIsCreateDialogOpen(true)}
            className="flex items-center gap-2"
          >
            <PlusIcon className="h-4 w-4" />
            <span className="hidden md:inline">Create</span>
          </Button>
        </div>
      </div>

      <ModelFamiliesList
        isLoading={isLoading}
        modelFamilies={modelFamilies}
        filteredFamilies={filteredAndSortedFamilies}
      />

      <CreateModelFamilyDialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen} />
    </div>
  );
}

function ModelFamiliesList({
  isLoading,
  modelFamilies,
  filteredFamilies,
}: {
  isLoading: boolean;
  modelFamilies?: ModelFamily[];
  filteredFamilies: ModelFamily[];
}) {
  if (isLoading) {
    return (
      <div className="py-8 text-center">
        <div className="text-muted-foreground">Loading model families...</div>
      </div>
    );
  }

  if (filteredFamilies.length === 0) {
    return (
      <div className="py-8 text-center">
        <div className="text-muted-foreground">
          {modelFamilies?.length === 0
            ? "No model families found. Create your first one!"
            : "No families match your current filters."}
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-2 @3xl:grid-cols-2 @5xl:grid-cols-3">
      {filteredFamilies.map((family) => (
        <ModelFamilyCard key={family.id} family={family} />
      ))}
    </div>
  );
}
